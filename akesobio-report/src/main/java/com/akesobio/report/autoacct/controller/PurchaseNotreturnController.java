package com.akesobio.report.autoacct.controller;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;

import com.akesobio.common.utils.validation.BigDecimalValidationUtils;
import com.akesobio.report.autoacct.utils.AutoAcctValidationUtils;
import com.akesobio.report.autoacct.mapper.AutoMaterialAcctFactoryMapper;
import com.akesobio.report.autoacct.mapper.AutoMaterialAcctMpmInfoMapper;
import com.akesobio.report.autoacct.mapper.MaterialMDMapper;

import com.akesobio.report.autoacct.domain.AutoMaterialAcctInventory;
import com.akesobio.report.autoacct.domain.AutoMaterialAcctPurchaseNotreturn;
import com.akesobio.report.autoacct.domain.excle.impot.ExcelImportPurchaseNotreturn;
import com.akesobio.report.autoacct.mapper.AutoMaterialAcctPurchaseNotreturnMapper;
import com.akesobio.report.autoacct.service.IAutoMaterialAcctPurchaseNotreturnService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.akesobio.common.annotation.Log;
import com.akesobio.common.core.controller.BaseController;
import com.akesobio.common.core.domain.AjaxResult;
import com.akesobio.common.enums.BusinessType;

import com.akesobio.common.utils.poi.ExcelUtil;
import com.akesobio.common.core.page.TableDataInfo;
import org.springframework.web.multipart.MultipartFile;

/**
 * SAP采购未回Controller
 * 
 * <AUTHOR>
 * @date 2024-03-19
 */
@RestController
@RequestMapping("/autoacct/notreturn")
public class PurchaseNotreturnController extends BaseController
{
    @Resource
    private IAutoMaterialAcctPurchaseNotreturnService autoMaterialAcctPurchaseNotreturnService;

    @Resource
    private com.akesobio.report.autoacct.mapper.AutoMaterialAcctInventoryMapper autoMaterialAcctInventoryMapper;

    @Resource
    private AutoMaterialAcctPurchaseNotreturnMapper autoMaterialAcctPurchaseNotreturnMapper;

    @Resource
    private AutoMaterialAcctFactoryMapper autoMaterialAcctFactoryMapper;

    @Resource
    private MaterialMDMapper materialMDMapper;

    @Resource
    private AutoMaterialAcctMpmInfoMapper autoMaterialAcctMpmInfoMapper;

    @Log(title = "采购未回导入", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    public AjaxResult importData(MultipartFile file, boolean updateSupport) throws Exception
    {
        ExcelUtil<ExcelImportPurchaseNotreturn> util = new ExcelUtil<ExcelImportPurchaseNotreturn>(ExcelImportPurchaseNotreturn.class);
        List<ExcelImportPurchaseNotreturn> userList = util.importExcel(file.getInputStream());
        
        // 加载校验数据
        AutoAcctValidationUtils.ValidationData validationData = AutoAcctValidationUtils.loadValidationData(
            autoMaterialAcctFactoryMapper, materialMDMapper, autoMaterialAcctMpmInfoMapper);
        
        // 数据校验
        BigDecimalValidationUtils.ValidationResult validationResult = BigDecimalValidationUtils.validateList(userList, (data, rowIndex) -> {
            List<BigDecimalValidationUtils.ValidationError> errors = new ArrayList<>();
            
            // 校验 purchasingUnitConversion 字段
            BigDecimalValidationUtils.ValidationError conversionError = BigDecimalValidationUtils.validateBigDecimal(
                data.getPurchasingUnitConversion(), "转换系数", rowIndex, true);
            if (conversionError != null) {
                errors.add(conversionError);
            }
            
            // 校验 purchasingUnitActualOrder 字段
            BigDecimalValidationUtils.ValidationError actualOrderError = BigDecimalValidationUtils.validateBigDecimal(
                data.getPurchasingUnitActualOrder(), "按采购单位下单数量", rowIndex, true);
            if (actualOrderError != null) {
                errors.add(actualOrderError);
            }
            
            // 校验 convertUnitUnreturnedQuantity 字段
            BigDecimalValidationUtils.ValidationError unreturnedError = BigDecimalValidationUtils.validateBigDecimal(
                data.getConvertUnitUnreturnedQuantity(), "换算为基本单位未回数量", rowIndex, true);
            if (unreturnedError != null) {
                errors.add(unreturnedError);
            }
            
            // 校验工厂代码
            BigDecimalValidationUtils.ValidationError factoryCodeError = AutoAcctValidationUtils.validateFactoryCodeFast(
                data.getFactoryCode(), validationData, "工厂代码", rowIndex);
            if (factoryCodeError != null) {
                errors.add(factoryCodeError);
            }
            
            // 校验物料编码
            BigDecimalValidationUtils.ValidationError materialError = AutoAcctValidationUtils.validateMaterialCodeFast(
                data.getMaterial(), validationData, "物料编码", rowIndex);
            if (materialError != null) {
                errors.add(materialError);
            }
            
            return errors.isEmpty() ? null : errors;
        });
        
        if (!validationResult.isValid()) {
            return error(validationResult.getErrorMessage());
        }
        
        String operName = getUsername();
        String message = autoMaterialAcctPurchaseNotreturnService.importData(userList, updateSupport, operName);
        return success(message);
    }

    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response)
    {
        ExcelUtil<ExcelImportPurchaseNotreturn> util = new ExcelUtil<ExcelImportPurchaseNotreturn>(ExcelImportPurchaseNotreturn.class);
        util.importTemplateExcel(response, "采购未回数据");
    }
    /**
     * 查询采购未回列表
     */
    @PreAuthorize("@ss.hasPermi('autoacct:notreturn:list')")
    @GetMapping("/list")
    public TableDataInfo list(AutoMaterialAcctPurchaseNotreturn autoMaterialAcctPurchaseNotreturn)
    {
        List<AutoMaterialAcctInventory> autoMaterialAcctInventoryList = autoMaterialAcctInventoryMapper.selectList(null);
        Map<String,AutoMaterialAcctInventory> materialAcctInventoryMap = autoMaterialAcctInventoryList
                .stream().collect(Collectors.toMap(autoMaterialAcctInventory -> autoMaterialAcctInventory.getId().toString(), autoMaterialAcctInventory -> autoMaterialAcctInventory));

        startPage();
        List<AutoMaterialAcctPurchaseNotreturn> list = autoMaterialAcctPurchaseNotreturnService.selectAutoMaterialAcctPurchaseNotreturnList(autoMaterialAcctPurchaseNotreturn);
        list =  list.stream().map(autoMaterialAcctPurchaseNotreturn1 ->{
            AutoMaterialAcctInventory autoMaterialAcctInventory = materialAcctInventoryMap.get(autoMaterialAcctPurchaseNotreturn1.getMaterialCode());
            if (autoMaterialAcctInventory != null) {
                autoMaterialAcctPurchaseNotreturn1.setUnit(autoMaterialAcctInventory.getBasicUnit());
                autoMaterialAcctPurchaseNotreturn1.setMaterialDesc(autoMaterialAcctInventory.getMaterialDesc());
                autoMaterialAcctPurchaseNotreturn1.setSpecification(autoMaterialAcctInventory.getPackingSpecifications());
                autoMaterialAcctPurchaseNotreturn1.setMaterialCode(autoMaterialAcctInventory.getMaterial());
            }
            return autoMaterialAcctPurchaseNotreturn1;
        } ).collect(Collectors.toList());

        return getDataTable(list);
    }

    @PreAuthorize("@ss.hasPermi('autoacct:notreturn:list')")
    @GetMapping("/listTest")
    public TableDataInfo listTest(AutoMaterialAcctPurchaseNotreturn autoMaterialAcctPurchaseNotreturn)
    {
        List<AutoMaterialAcctInventory> autoMaterialAcctInventoryList = autoMaterialAcctInventoryMapper.selectList(null);
        Map<String,AutoMaterialAcctInventory> materialAcctInventoryMap = autoMaterialAcctInventoryList
                .stream().collect(Collectors.toMap(autoMaterialAcctInventory -> autoMaterialAcctInventory.getId().toString(), autoMaterialAcctInventory -> autoMaterialAcctInventory));

        startPage();
        List<AutoMaterialAcctPurchaseNotreturn> list = autoMaterialAcctPurchaseNotreturnMapper.selectAutoMaterialAcctPurchaseNotreturnTestList(autoMaterialAcctPurchaseNotreturn);
        list =  list.stream().map(autoMaterialAcctPurchaseNotreturn1 ->{
            AutoMaterialAcctInventory autoMaterialAcctInventory = materialAcctInventoryMap.get(autoMaterialAcctPurchaseNotreturn1.getMaterialCode());
            if (autoMaterialAcctInventory != null) {
                autoMaterialAcctPurchaseNotreturn1.setUnit(autoMaterialAcctInventory.getBasicUnit());
                autoMaterialAcctPurchaseNotreturn1.setMaterialDesc(autoMaterialAcctInventory.getMaterialDesc());
                autoMaterialAcctPurchaseNotreturn1.setSpecification(autoMaterialAcctInventory.getPackingSpecifications());
                autoMaterialAcctPurchaseNotreturn1.setMaterialCode(autoMaterialAcctInventory.getMaterial());
            }
            return autoMaterialAcctPurchaseNotreturn1;
        } ).collect(Collectors.toList());

        return getDataTable(list);
    }


    /**
     * 导出采购未回列表
     */
    @PreAuthorize("@ss.hasPermi('autoacct:notreturn:export')")
    @Log(title = "采购未回", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, AutoMaterialAcctPurchaseNotreturn autoMaterialAcctPurchaseNotreturn)
    {
        List<AutoMaterialAcctPurchaseNotreturn> list = autoMaterialAcctPurchaseNotreturnService.selectAutoMaterialAcctPurchaseNotreturnList(autoMaterialAcctPurchaseNotreturn);
        ExcelUtil<AutoMaterialAcctPurchaseNotreturn> util = new ExcelUtil<AutoMaterialAcctPurchaseNotreturn>(AutoMaterialAcctPurchaseNotreturn.class);
        util.exportExcel(response, list, "采购未回数据");
    }

    /**
     * 获取采购未回详细信息
     */
    @PreAuthorize("@ss.hasPermi('autoacct:notreturn:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {

        AutoMaterialAcctPurchaseNotreturn autoMaterialAcctPurchaseNotreturn = autoMaterialAcctPurchaseNotreturnService.selectAutoMaterialAcctPurchaseNotreturnById(id);
        LambdaQueryWrapper<AutoMaterialAcctInventory> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(AutoMaterialAcctInventory::getMaterial,autoMaterialAcctPurchaseNotreturn.getMaterial());
        lambdaQueryWrapper.eq(AutoMaterialAcctInventory::getFactory,autoMaterialAcctPurchaseNotreturn.getFactoryCode());
        List<AutoMaterialAcctInventory> autoMaterialAcctInventoryList = autoMaterialAcctInventoryMapper.selectList(lambdaQueryWrapper);
        if (autoMaterialAcctInventoryList != null&&autoMaterialAcctInventoryList.get(0) != null) {
            AutoMaterialAcctInventory autoMaterialAcctInventory = autoMaterialAcctInventoryList.get(0);
            autoMaterialAcctPurchaseNotreturn.setUnit(autoMaterialAcctInventory.getBasicUnit());
//            autoMaterialAcctPurchaseNotreturn.setMaterialDesc(autoMaterialAcctInventory.getMaterialDesc());
            autoMaterialAcctPurchaseNotreturn.setSpecification(autoMaterialAcctInventory.getPackingSpecifications());
//            autoMaterialAcctPurchaseNotreturn.setMaterialCode(autoMaterialAcctInventory.getMaterial());
        }
        return success(autoMaterialAcctPurchaseNotreturn);
    }

    /**
     * 新增采购未回
     */
    @PreAuthorize("@ss.hasPermi('autoacct:notreturn:add')")
    @Log(title = "采购未回", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody AutoMaterialAcctPurchaseNotreturn autoMaterialAcctPurchaseNotreturn)
    {

//        LambdaQueryWrapper<AutoMaterialAcctPurchaseNotreturn> lambdaQueryWrapper2 = new LambdaQueryWrapper<>();
//        lambdaQueryWrapper2.eq(AutoMaterialAcctPurchaseNotreturn::getMaterialCode,autoMaterialAcctPurchaseNotreturn.getMaterialCode())
//                .eq(AutoMaterialAcctPurchaseNotreturn::getPlanDate,autoMaterialAcctPurchaseNotreturn.getPlanDate());

//        AutoMaterialAcctPurchaseNotreturn temp =autoMaterialAcctPurchaseNotreturnService.getOne(lambdaQueryWrapper2);
//        if (temp == null){
            autoMaterialAcctPurchaseNotreturnService.save(autoMaterialAcctPurchaseNotreturn);
//        }else {
//            autoMaterialAcctPurchaseNotreturn.setId(temp.getId());
            autoMaterialAcctPurchaseNotreturnService.updateById(autoMaterialAcctPurchaseNotreturn);
//        }
//        autoMaterialAcctPurchaseNotreturnService.calculateInventory(autoMaterialAcctPurchaseNotreturn);
        return success();
    }

    /**
     * 修改采购未回
     */
    @PreAuthorize("@ss.hasPermi('autoacct:notreturn:edit')")
    @Log(title = "采购未回", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody AutoMaterialAcctPurchaseNotreturn autoMaterialAcctPurchaseNotreturn)
    {
        return toAjax(autoMaterialAcctPurchaseNotreturnService.updateAutoMaterialAcctPurchaseNotreturn(autoMaterialAcctPurchaseNotreturn));
    }

    /**
     * 删除采购未回
     */
    @PreAuthorize("@ss.hasPermi('autoacct:notreturn:remove')")
    @Log(title = "采购未回", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(autoMaterialAcctPurchaseNotreturnService.deleteAutoMaterialAcctPurchaseNotreturnByIds(ids));
    }
}
