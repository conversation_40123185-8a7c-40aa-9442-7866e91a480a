package com.akesobio.report.autoacct.service.impl;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

import com.akesobio.common.exception.ServiceException;
import com.akesobio.common.utils.StringUtils;
import com.akesobio.report.autoacct.domain.MpmInfo;
import com.akesobio.report.autoacct.domain.ProdPlan;
import com.akesobio.report.autoacct.mapper.AutoMaterialAcctMpmInfoMapper;
import com.akesobio.report.autoacct.mapper.AutoMaterialAcctProdPlanMapper;
import com.akesobio.report.autoacct.service.IAutoMaterialAcctProdPlanService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;


/**
 * 生产计划Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-02-26
 */
@Service
public class AutoMaterialAcctProdPlanServiceImpl extends ServiceImpl<AutoMaterialAcctProdPlanMapper, ProdPlan> implements IAutoMaterialAcctProdPlanService
{
    @Resource
    private AutoMaterialAcctProdPlanMapper autoMaterialAcctProdPlanMapper;
    @Resource
    private AutoMaterialAcctMpmInfoMapper autoMaterialAcctMpmInfoMapper;
    public String importProdPlan(List<ProdPlan> userList, Boolean isUpdateSupport, String operName) throws ParseException {
        if (StringUtils.isNull(userList) || userList.size() == 0)
        {
            throw new ServiceException("导入用户数据不能为空！");
        }
        int successNum = 0;
        int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        LambdaQueryWrapper<MpmInfo> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        List<MpmInfo> list = autoMaterialAcctMpmInfoMapper.selectList(lambdaQueryWrapper);
        Map<String, MpmInfo> stringAutoMaterialAcctMpmInfoMap = list.stream().collect(Collectors.toMap(MpmInfo::getMpmCode, Function.identity()));

        for (ProdPlan user : userList)
        {
//
            try
            {
//
                if(stringAutoMaterialAcctMpmInfoMap.containsKey(user.getMpmCode())){
                    save(user);
                    successMsg.append("<br/>" + successNum + "、生产计划： " + user.toString() + " 导入成功");
                }else {
                    failureNum++;
                    String msg = "<br/>" + failureNum + "、mpm代码： " + user.getMpmCode() + "不存在";
                    failureMsg.append(msg);
                }
            }
            catch (Exception e)
            {
                failureNum++;
                String msg = "<br/>" + failureNum + "、mpm代码： " + user.getMpmCode() + " 导入失败：";
                failureMsg.append(msg + e.getMessage());
                log.error(msg, e);
            }
        }
        if (failureNum > 0)
        {
            failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据格式不正确，错误如下：");
            throw new ServiceException(failureMsg.toString());
        }
        else
        {
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条，数据如下：");
        }
        return successMsg.toString();
    }
    /**
     * 查询生产计划
     * 
     * @param id 生产计划主键
     * @return 生产计划
     */
    @Override
    public ProdPlan selectAutoMaterialAcctProdPlanById(Long id)
    {
        return autoMaterialAcctProdPlanMapper.selectAutoMaterialAcctProdPlanById(id);
    }

    /**
     * 查询生产计划列表
     * 
     * @param prodPlan 生产计划
     * @return 生产计划
     */
    @Override
    public List<ProdPlan> selectAutoMaterialAcctProdPlanList(ProdPlan prodPlan)
    {
        return autoMaterialAcctProdPlanMapper.selectAutoMaterialAcctProdPlanList(prodPlan);
    }

    /**
     * 新增生产计划
     * 
     * @param prodPlan 生产计划
     * @return 结果
     */
    @Override
    public int insertAutoMaterialAcctProdPlan(ProdPlan prodPlan)
    {
        return autoMaterialAcctProdPlanMapper.insertAutoMaterialAcctProdPlan(prodPlan);
    }

    /**
     * 修改生产计划
     * 
     * @param prodPlan 生产计划
     * @return 结果
     */
    @Override
    public int updateAutoMaterialAcctProdPlan(ProdPlan prodPlan)
    {
        return autoMaterialAcctProdPlanMapper.updateAutoMaterialAcctProdPlan(prodPlan);
    }

    /**
     * 批量删除生产计划
     * 
     * @param ids 需要删除的生产计划主键
     * @return 结果
     */
    @Override
    public int deleteAutoMaterialAcctProdPlanByIds(Long[] ids)
    {
        return autoMaterialAcctProdPlanMapper.deleteAutoMaterialAcctProdPlanByIds(ids);
    }

    /**
     * 删除生产计划信息
     * 
     * @param id 生产计划主键
     * @return 结果
     */
    @Override
    public int deleteAutoMaterialAcctProdPlanById(Long id)
    {
        return autoMaterialAcctProdPlanMapper.deleteAutoMaterialAcctProdPlanById(id);
    }
}
