package com.akesobio.report.autoacct.service.impl;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

import com.akesobio.common.exception.ServiceException;
import com.akesobio.common.utils.StringUtils;
import com.akesobio.report.autoacct.domain.MpmInfo;
import com.akesobio.report.autoacct.domain.ProdPlan;
import com.akesobio.report.autoacct.mapper.AutoMaterialAcctMpmInfoMapper;
import com.akesobio.report.autoacct.mapper.AutoMaterialAcctProdPlanMapper;
import com.akesobio.report.autoacct.service.IAutoMaterialAcctProdPlanService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;


/**
 * 生产计划Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-02-26
 */
@Service
public class AutoMaterialAcctProdPlanServiceImpl extends ServiceImpl<AutoMaterialAcctProdPlanMapper, ProdPlan> implements IAutoMaterialAcctProdPlanService
{
    @Resource
    private AutoMaterialAcctProdPlanMapper autoMaterialAcctProdPlanMapper;
    @Resource
    private AutoMaterialAcctMpmInfoMapper autoMaterialAcctMpmInfoMapper;
    public String importProdPlan(List<ProdPlan> userList, Boolean isUpdateSupport, String operName) throws ParseException {
        if (StringUtils.isNull(userList) || userList.size() == 0)
        {
            throw new ServiceException("导入生产计划数据不能为空！");
        }
        int successNum = 0;
        int failureNum = 0;
        int updateNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();

        // 加载MPM编码验证数据
        LambdaQueryWrapper<MpmInfo> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        List<MpmInfo> list = autoMaterialAcctMpmInfoMapper.selectList(lambdaQueryWrapper);
        Map<String, MpmInfo> stringAutoMaterialAcctMpmInfoMap = list.stream().collect(Collectors.toMap(MpmInfo::getMpmCode, Function.identity()));

        for (ProdPlan prodPlan : userList)
        {
            try
            {
                // 验证MPM代码是否存在
                if (!stringAutoMaterialAcctMpmInfoMap.containsKey(prodPlan.getMpmCode())) {
                    failureNum++;
                    String msg = "<br/>" + failureNum + "、MPM代码： " + prodPlan.getMpmCode() + " 不存在";
                    failureMsg.append(msg);
                    continue;
                }

                // 根据行号（ID）判断是新增还是更新
                if (prodPlan.getId() != null && prodPlan.getId() > 0) {
                    // 更新操作
                    if (isUpdateSupport) {
                        ProdPlan existingRecord = getById(prodPlan.getId());
                        if (existingRecord != null) {
                            // 保留原有的创建信息，更新其他字段
                            prodPlan.setCreationTime(existingRecord.getCreationTime());
                            prodPlan.setCreatedBy(existingRecord.getCreatedBy());
                            // 设置更新信息
                            prodPlan.setUpdated(System.currentTimeMillis());
                            prodPlan.setUpdater(operName);

                            updateById(prodPlan);
                            updateNum++;
                        } else {
                            failureNum++;
                            String msg = "<br/>" + failureNum + "、行号：" + prodPlan.getId() + " 对应的记录不存在";
                            failureMsg.append(msg);
                            continue;
                        }
                    } else {
                        failureNum++;
                        String msg = "<br/>" + failureNum + "、行号：" + prodPlan.getId() + " 已存在，但未开启更新模式";
                        failureMsg.append(msg);
                        continue;
                    }
                } else {
                    // 新增操作 - 检查是否存在相同的记录（基于业务唯一性：MPM代码+生产日期+生产批次）
                    LambdaQueryWrapper<ProdPlan> queryWrapper = new LambdaQueryWrapper<>();
                    queryWrapper.eq(ProdPlan::getMpmCode, prodPlan.getMpmCode())
                               .eq(ProdPlan::getProductMonth, prodPlan.getProductMonth())
                               .eq(ProdPlan::getProducBatch, prodPlan.getProducBatch());
                    ProdPlan existingPlan = getOne(queryWrapper);

                    if (existingPlan != null) {
                        // 如果允许更新，则更新现有记录
                        if (isUpdateSupport) {
                            prodPlan.setId(existingPlan.getId());
                            // 保留原有的创建信息
                            prodPlan.setCreationTime(existingPlan.getCreationTime());
                            prodPlan.setCreatedBy(existingPlan.getCreatedBy());
                            // 设置更新信息
                            prodPlan.setUpdated(System.currentTimeMillis());
                            prodPlan.setUpdater(operName);

                            updateById(prodPlan);
                            updateNum++;
                        } else {
                            failureNum++;
                            String msg = "<br/>" + failureNum + "、MPM代码：" + prodPlan.getMpmCode() +
                                       " 生产日期：" + prodPlan.getProductMonth() +
                                       " 生产批次：" + prodPlan.getProducBatch() + " 的数据已存在且不允许更新";
                            failureMsg.append(msg);
                            continue;
                        }
                    } else {
                        // 新增记录
                        prodPlan.setCreationTime(System.currentTimeMillis());
                        prodPlan.setCreatedBy(operName);
                        save(prodPlan);
                        successNum++;
                    }
                }
            }
            catch (Exception e)
            {
                failureNum++;
                String msg = "<br/>" + failureNum + "、MPM代码： " + prodPlan.getMpmCode() + " 导入失败：";
                failureMsg.append(msg + e.getMessage());
                log.error(msg, e);
            }
        }

        if (failureNum > 0)
        {
            failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据格式不正确，错误如下：");
            throw new ServiceException(failureMsg.toString());
        }
        else
        {
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + (successNum + updateNum) + " 条，其中新增 " + successNum + " 条，更新 " + updateNum + " 条");
        }
        return successMsg.toString();
    }
    /**
     * 查询生产计划
     * 
     * @param id 生产计划主键
     * @return 生产计划
     */
    @Override
    public ProdPlan selectAutoMaterialAcctProdPlanById(Long id)
    {
        return autoMaterialAcctProdPlanMapper.selectAutoMaterialAcctProdPlanById(id);
    }

    /**
     * 查询生产计划列表
     * 
     * @param prodPlan 生产计划
     * @return 生产计划
     */
    @Override
    public List<ProdPlan> selectAutoMaterialAcctProdPlanList(ProdPlan prodPlan)
    {
        return autoMaterialAcctProdPlanMapper.selectAutoMaterialAcctProdPlanList(prodPlan);
    }

    /**
     * 新增生产计划
     * 
     * @param prodPlan 生产计划
     * @return 结果
     */
    @Override
    public int insertAutoMaterialAcctProdPlan(ProdPlan prodPlan)
    {
        return autoMaterialAcctProdPlanMapper.insertAutoMaterialAcctProdPlan(prodPlan);
    }

    /**
     * 修改生产计划
     * 
     * @param prodPlan 生产计划
     * @return 结果
     */
    @Override
    public int updateAutoMaterialAcctProdPlan(ProdPlan prodPlan)
    {
        return autoMaterialAcctProdPlanMapper.updateAutoMaterialAcctProdPlan(prodPlan);
    }

    /**
     * 批量删除生产计划
     * 
     * @param ids 需要删除的生产计划主键
     * @return 结果
     */
    @Override
    public int deleteAutoMaterialAcctProdPlanByIds(Long[] ids)
    {
        return autoMaterialAcctProdPlanMapper.deleteAutoMaterialAcctProdPlanByIds(ids);
    }

    /**
     * 删除生产计划信息
     * 
     * @param id 生产计划主键
     * @return 结果
     */
    @Override
    public int deleteAutoMaterialAcctProdPlanById(Long id)
    {
        return autoMaterialAcctProdPlanMapper.deleteAutoMaterialAcctProdPlanById(id);
    }
}
